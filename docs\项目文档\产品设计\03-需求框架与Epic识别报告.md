# 需求框架与Epic识别报告

---

## 1. 文档信息

| 属性 | 值 |
| :--- | :--- |
| **项目名称** | 茂名市地质灾害预警平台 |
| **文档版本** | V1.4 |
| **文档状态** | 已完成 |
| **创建日期** | 2025-07-05 |
| **最后更新日期** | 2025-07-13 |
| **作者** | 梁铭显 |
| **审核者** | 待定 |

---

## 2. 修订历史

| 版本号 | 修订日期 | 修订内容摘要 | 修订人 |
| :--- | :--- | :--- | :--- |
| V1.0 | 2025-07-05 | 创建初始版本 | 梁铭显 |
| V1.1 | 2025-07-07 | 基于成本分析调整预警发布机制优先级，强化微信公众号功能 | 梁铭显 |
| V1.2 | 2025-07-10 | 新增系统管理模块Epic，完善用户管理和权限管理功能设计 | 梁铭显 |
| V1.3 | 2025-07-12 | 新增Feature 1.3预警信息管理功能，增强公众查询服务Epic | 梁铭显 |
| V1.4 | 2025-07-13 | 新增Feature 4.6安全防护机制，强化系统管理模块安全功能 | 梁铭显 |

---

## 3. 执行摘要

### 3.1. 需求框架概述
基于市场研究和产品愿景，茂名市地质灾害预警平台的核心需求框架围绕四大业务域展开：公众查询服务、数据管理模块、预警发布机制、系统管理模块。其中公众查询服务是当前最急需的功能，需要在2周内快速实现。

### 3.2. Epic概览
识别出4个核心Epic和12个主要Feature（包含细分的预警发布功能和系统管理功能），按照业务价值、紧迫性和成本效益进行优先级排序，为后续迭代开发提供清晰的功能框架。

### 3.3. 关键洞察
*   公众查询服务是核心价值所在，应优先开发
*   基于天地图服务可以快速实现查询功能
*   数据管理和预警发布为支撑功能，分阶段实现
*   系统管理模块是安全保障，用户认证和权限管理需要优先实现

---

## 4. 需求框架提取

### 4.1. 业务需求分析
基于用户研究和产品愿景，提取出以下核心业务需求：

**4.1.1. 公众服务需求**
*   地质灾害风险查询：群众需要便捷查询居住地或关注地点的地质灾害风险
*   预警信息接收：群众需要及时接收地质灾害预警信息
*   信息透明化：让地质灾害风险信息公开透明，提升公众安全意识

**4.1.2. 政府管理需求**
*   数据信息化管理：将离线的地质灾害点和风险防范区数据进行信息化管理
*   预警信息发布：建立多渠道预警发布机制，直接向群众发布预警信息
*   工作效率提升：通过信息化手段提升地质灾害防治工作效率

**4.1.3. 系统功能需求**
*   数据存储与管理：安全可靠的数据存储和管理能力
*   地图服务集成：基于天地图等地图服务提供空间查询能力
*   多渠道发布：支持网站、微信公众号、短信等多种发布渠道

### 4.2. 需求框架分类

**4.2.1. 核心业务框架**
*   **公众查询服务框架**：提供便民化的地质灾害风险查询服务
*   **数据管理框架**：提供地质灾害数据的信息化管理能力
*   **预警发布框架**：提供多渠道的预警信息发布能力

**4.2.2. 支撑技术框架**
*   **地图服务框架**：基于天地图地图服务，按地质灾害规范颜色显示图层
*   **数据存储框架**：
    *   MySQL：存储业务数据（用户信息、预警记录、操作日志等）
    *   MongoDB：存储GEO矢量数据（地质灾害点和风险防范区的面类型矢量数据）
*   **数据处理框架**：
    *   SHP格式数据导入处理，删除涉密数据后存储到数据库
    *   矢量数据标准化处理，支持点类型和面类型几何数据
*   **技术栈框架**：
    *   后端：Python FastAPI框架
    *   前端：Vue3 + Element Plus
    *   构建工具：Vite
*   **用户管理框架**：支持内部用户和公众用户的管理
*   **基础设施框架**：
    *   服务器：16核64G云服务器，Ubuntu 24.04系统
    *   存储：系统盘100GB ESSD PL1，数据盘500GB增强型SSD
    *   网络：30Mbps固定带宽

---

## 5. Epic识别与定义

### 5.1. Epic 1: 公众查询服务
**Epic描述：** 为茂名市地质灾害点及风险防范区所在镇街的群众提供便民化的地质灾害风险查询服务

**业务价值：**
*   让群众随时随地了解身边的地质灾害风险
*   提升公众安全防护意识
*   实现地质灾害风险信息的透明化

**用户故事概述：**
*   作为茂名市群众，我希望能够通过网站查询我所在位置的地质灾害风险
*   作为茂名市群众，我希望能够通过微信公众号查询地质灾害风险信息
*   作为茂名市群众，我希望查询结果准确、响应快速

**验收标准：**
*   支持基于位置的地质灾害风险查询
*   查询响应时间<3秒
*   查询结果准确率100%
*   支持网站和微信公众号两种查询方式

### 5.2. Epic 2: 数据管理模块
**Epic描述：** 建立地质灾害点和风险防范区的信息化管理模块

**业务价值：**
*   将离线数据转为在线管理，提升管理效率
*   为公众查询和预警发布提供数据支撑
*   建立标准化的数据管理流程

**用户故事概述：**
*   作为防灾科工作人员，我希望能够在线管理地质灾害点数据
*   作为防灾科工作人员，我希望能够管理风险防范区数据
*   作为防灾科工作人员，我希望系统操作简单、数据安全可靠

**验收标准：**
*   支持74215个地质灾害点及风险防范区数据管理
*   提供数据增删改查功能
*   数据安全性和完整性保障

### 5.3. Epic 3: 预警发布机制
**Epic描述：** 建立多渠道的地质灾害预警信息发布机制

**业务价值：**
*   扩大预警信息覆盖面，直接向群众发布预警
*   提升预警信息发布效率
*   建立标准化的预警发布流程

**用户故事概述：**
*   作为防灾科工作人员，我希望能够通过系统向全市或指定区域发布预警信息
*   作为防灾科工作人员，我希望能够通过多种渠道发布预警信息
*   作为茂名市民，我希望能够及时收到地质灾害预警信息

**验收标准：**
*   支持通过微信公众号发布预警信息
*   支持向全市或指定镇街发布预警
*   预警发布覆盖率95%+

### 5.4. Epic 4: 系统管理模块
**Epic描述：** 建立完整的系统管理体系，包括用户认证、权限管理、操作审计和系统配置，确保平台的安全性、可管理性和可维护性

**业务价值：**
*   保障系统和数据安全，满足政府系统的安全合规要求
*   建立标准化的用户和权限管理流程，提升管理效率
*   提供完整的操作审计能力，支持责任追溯和合规检查
*   简化系统维护工作，提升系统的可维护性和稳定性

**用户故事概述：**
*   作为系统管理员，我希望能够安全地管理系统用户和权限
*   作为系统管理员，我希望能够对用户进行增加、修改、查询、停用等管理操作
*   作为防灾科工作人员，我希望能够安全便捷地使用系统功能
*   作为防灾科科长，我希望能够了解系统的使用情况和操作记录
*   作为审计人员，我希望能够获取完整的系统操作审计信息

**验收标准：**
*   支持10-20个内部用户的完整生命周期管理
*   提供完整的用户管理功能（增改查、启停用）
*   提供完整的操作日志记录和查询功能
*   系统配置管理界面友好，操作简便
*   安全机制完善，满足政府系统安全要求
*   系统运行稳定，维护成本低

---

## 6. Feature分解

### 6.1. Epic 1: 公众查询服务 - Feature分解
*   **Feature 1.1: 网站查询服务（自适应响应）**
    *   基于天地图的地图展示
    *   预警信息列表展示和查询
    *   区域筛选功能
    *   图层管理工具条
    *   茂名市自然资源局微信公众号菜单设计和跳转

*   **Feature 1.2: 查询结果展示**
    *   地质灾害点信息图层展示（按地质灾害规范颜色显示）
    *   风险防范区信息图层展示（按地质灾害规范颜色显示）
    *   单个地质灾害点和风险防范区的详细风险信息说明

*   **Feature 1.3: 预警信息管理**
    *   历史预警信息列表展示
    *   预警信息分页显示
    *   预警信息详情查看
    *   预警信息区域筛选
    *   地图位置联动功能

### 6.2. Epic 2: 数据管理模块 - Feature分解
*   **Feature 2.1: 地质灾害点管理**
    *   灾害点基础信息管理
    *   灾害点位置信息管理
    *   灾害点状态管理

*   **Feature 2.2: 风险防范区管理**
    *   防范区基础信息管理
    *   防范区位置信息管理
    *   防范区状态管理

*   **Feature 2.3: 数据导入导出**
    *   SHP格式数据导入功能（不导入涉密数据）
    *   矢量数据点类型和面类型处理
    *   批量数据导入功能
    *   数据导出功能
    *   数据备份恢复

### 6.3. Epic 3: 预警发布机制 - Feature分解
*   **Feature 3.1: 预警信息管理**
    *   预警信息编辑发布
    *   预警等级管理
    *   预警历史记录

*   **Feature 3.2: 多渠道发布**
    *   微信公众号发布（优先级：P1，成本低，覆盖面广）
    *   短信发布（优先级：P3，成本高，作为应急补充手段）
    *   发布状态跟踪

### 6.4. Epic 4: 系统管理模块 - Feature分解
*   **Feature 4.1: 用户认证与授权**
    *   用户登录认证功能（用户名密码+短信验证码多因子认证）
    *   会话管理和自动过期（15分钟无操作自动退出）
    *   登录安全控制（失败次数限制、账户锁定、IP白名单限制）
    *   实名制用户管理和手机号码验证

*   **Feature 4.2: 用户管理**
    *   用户信息管理（新增、修改、查询用户）
    *   用户状态管理（启用、停用、锁定、解锁）
    *   用户密码重置和管理
    *   用户角色分配和调整

*   **Feature 4.3: 权限管理**
    *   角色定义和管理（管理员、科长、操作员等）
    *   功能权限分配（查看、新增、修改、删除、导出等）
    *   权限变更日志和审计
    *   IP白名单管理
    *   最小权限原则实施和定期权限审查

*   **Feature 4.4: 操作日志管理**
    *   系统操作日志记录（登录、数据操作、权限变更等）
    *   日志查询和筛选功能
    *   日志导出和备份
    *   日志安全存储和防篡改

*   **Feature 4.5: 系统配置管理**
    *   系统基础参数配置
    *   配置变更版本管理

*   **Feature 4.6: 安全防护机制**
    *   预警发布双重验证（发布人短信验证+审核人动态验证码）
    *   内容审核和敏感词过滤
    *   安全事件监控和应急响应

---

## 7. 需求框架优先级评估

### 7.1. 优先级评估矩阵

| Epic/Feature | 业务价值 | 用户需求紧迫性 | 技术实现难度 | 资源需求 | 综合优先级 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| Epic 1: 公众查询服务 | 高 | 高 | 中 | 中 | P0 |
| Feature 1.1: 网站查询服务（自适应响应） | 高 | 高 | 低 | 中 | P0 |
| Feature 1.2: 查询结果展示 | 高 | 高 | 中 | 中 | P0 |
| Epic 4: 系统管理模块 | 高 | 高 | 中 | 中 | P1 |
| Feature 4.1: 用户认证与授权 | 高 | 高 | 中 | 中 | P1 |
| Feature 4.2: 用户管理 | 高 | 高 | 中 | 中 | P1 |
| Feature 4.3: 权限管理 | 高 | 高 | 中 | 中 | P1 |
| Epic 2: 数据管理模块 | 高 | 中 | 中 | 中 | P1 |
| Feature 2.1: 地质灾害点管理 | 高 | 中 | 中 | 中 | P1 |
| Feature 2.2: 风险防范区管理 | 高 | 中 | 中 | 中 | P1 |
| Feature 4.4: 操作日志管理 | 中 | 中 | 中 | 中 | P2 |
| Feature 2.3: 数据导入导出 | 高 | 中 | 中 | 中 | P2 |
| Epic 3: 预警发布机制 | 中 | 中 | 中 | 中 | P2 |
| Feature 3.1: 预警信息管理 | 高 | 高 | 中 | 高 | P2 |
| Feature 3.2.1: 微信公众号发布 | 高 | 高 | 中 | 低 | P2 |
| Feature 4.5: 系统配置管理 | 中 | 低 | 中 | 中 | P3 |
| Feature 4.6: 安全防护机制 | 高 | 高 | 中 | 中 | P1 |
| Feature 3.2.2: 短信发布 | 中 | 中 | 低 | 高 | P3 |

### 7.2. 优先级说明
*   **P0 (最高优先级)：** 公众查询服务，需要在2周内完成
*   **P1 (高优先级)：** 系统管理模块（用户认证、用户管理和权限管理）和数据管理模块，需要在10周内完成
*   **P2 (中优先级)：** 操作日志管理、数据导入导出和预警信息管理功能，需要在13周内完成
*   **P3 (低优先级)：** 系统配置管理和短信发送功能，作为补充功能，可延后实现

---

## 8. 框架可行性初评

### 8.1. 技术可行性
*   **公众查询服务：** 基于天地图服务，技术成熟，技术难度低
*   **系统管理模块：** 基于成熟的认证和权限管理框架，技术难度中等
*   **数据管理模块：** 基于常规CRUD操作以及导入导出功能，技术难度中等
*   **预警发布机制：** 需要集成第三方服务，技术难度中等

### 8.2. 资源可行性
*   **开发资源：** 需要3-5人的开发团队，资源需求合理
*   **时间资源：** 分阶段实现，时间安排可行
*   **技术资源：** 主要依赖云服务和第三方API，成本可控

### 8.3. 业务可行性
*   **用户接受度：** 公众查询服务满足实际需求，接受度高
*   **管理可行性：** 数据管理模块符合工作流程，可行性强
*   **运营可行性：** 系统维护成本低，运营可持续

---

## 9. 迭代准备输入

### 9.1. 第一迭代（2周内）
**目标：** 快速实现公众查询服务
**输入Epic：** Epic 1: 公众查询服务
**关键Feature：**
*   Feature 1.1: 网站查询服务（自适应响应）
*   Feature 1.2: 查询结果展示

### 9.2. 第二迭代（10周内）
**目标：** 建立系统管理和数据管理体系
**输入Epic：** Epic 4: 系统管理模块 + Epic 2: 数据管理模块
**关键Feature：**
*   Feature 4.1: 用户认证与授权
*   Feature 4.2: 用户管理
*   Feature 4.3: 权限管理
*   Feature 4.6: 安全防护机制
*   Feature 2.1: 地质灾害点管理
*   Feature 2.2: 风险防范区管理

### 9.3. 第三迭代（13周内）
**目标：** 完善系统管理和预警发布机制
**输入Epic：** Epic 4: 系统管理模块 + Epic 2: 数据管理模块 + Epic 3: 预警发布机制
**关键Feature：**
*   Feature 4.4: 操作日志管理
*   Feature 2.3: 数据导入导出
*   Feature 3.1: 预警信息管理
*   Feature 3.2: 多渠道发布

---

## 10. 风险识别与应对

### 10.1. 主要风险
*   **技术风险：** 天地图服务集成可能遇到技术问题
*   **数据风险：** 现有数据质量可能影响系统效果
*   **用户风险：** 公众接受度可能低于预期
*   **安全风险：** 系统管理模块的安全机制可能存在漏洞

### 10.2. 应对策略
*   **技术风险：** 提前进行技术验证，准备备选方案
*   **数据风险：** 加强数据质量检查和清洗
*   **用户风险：** 加强用户宣传和培训
*   **安全风险：** 采用成熟的安全框架，进行安全测试和代码审查
*   **时间风险：** 采用分阶段实现，优先实现核心功能

---

## 11. 总结与建议

### 11.1. 核心建议
*   优先开发公众查询服务，快速满足用户最急需的需求
*   采用敏捷开发模式，分阶段迭代实现
*   重视用户体验设计，确保系统简单易用
*   建立完善的测试和质量保障机制

### 11.2. 成功关键因素
*   快速交付公众查询服务，建立用户信心
*   确保数据准确性和系统稳定性
*   持续收集用户反馈，优化产品功能

---

**注：本报告为后续迭代准备阶段提供输入，将根据开发进展持续更新。**

